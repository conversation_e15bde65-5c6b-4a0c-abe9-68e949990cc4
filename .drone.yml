
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

kind: pipeline
name: hyperstream-cicd

environment:
  DOCKER_BUILDKIT: 1
  OPENSSL_CONF: /etc/ssl/

image_pull_secrets:
- containers_dev_config

steps:

- name: build-hyperstream
  image: containers-dev.apporto.com/images/hyperstream-builder:2024.2
  network_mode: bridge
  volumes:
  - name: m2_cache
    path: /root/.m2/
  commands:
  - mvn clean package

# Docker for branches/testing
- name: build-hyperstream-docker
  image: plugins/docker
  network_mode: bridge
  environment:
    OPENSSL_CONF: /etc/ssl/
  settings:
    tags:
      - ${DRONE_SOURCE_BRANCH//\//-}
      - ${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    username:
      from_secret: CONTAINERS_DEV_USER
    password:
      from_secret: CONTAINERS_DEV_PASS
    repo: containers-dev.apporto.com/images/hyperstream
    registry: containers-dev.apporto.com
    dockerfile: Dockerfile.apporto
    build_args:
      - GIT_COMMIT=${DRONE_COMMIT_SHA}
      - GIT_TAGS=${DRONE_TAG}
    build_args_from_env:
      - DRONE_REPO_NAME
      - DRONE_SOURCE_BRANCH
      - DRONE_COMMIT_SHA
      - DRONE_TAG
      - DRONE_COMMIT_REF
      - DRONE_BUILD_NUMBER
      - APP_BUILD_VERSION
  when:
    event:
      exclude:
        - tag

- name: build-hyperstream-db-docker
  image: plugins/docker
  network_mode: bridge
  settings:
    tags:
      - ${DRONE_SOURCE_BRANCH//\//-}
      - ${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    username:
      from_secret: CONTAINERS_DEV_USER
    password:
      from_secret: CONTAINERS_DEV_PASS
    repo: containers-dev.apporto.com/images/hyperstream-db
    registry: containers-dev.apporto.com
    dockerfile: Dockerfile.apporto_mysql
  when:
    event:
      exclude:
        - tag


- name: copy-to-nexus
  image: containers-dev.apporto.com/tools/skopeo
  environment:
    CONTAINERS_DEV_USER:
      from_secret: CONTAINERS_DEV_USER
    CONTAINERS_DEV_PASS:
      from_secret: CONTAINERS_DEV_PASS
    DOCKER_USER:
      from_secret: DOCKER_USER
    DOCKER_PASS:
      from_secret: DOCKER_PASS
  commands:
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream:${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8} 
      docker://docker.apporto.com/hyperstream:${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream:${DRONE_SOURCE_BRANCH//\//-} 
      docker://docker.apporto.com/hyperstream:${DRONE_SOURCE_BRANCH//\//-}
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream-db:${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8} 
      docker://docker.apporto.com/hyperstream-db:${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream-db:${DRONE_SOURCE_BRANCH//\//-} 
      docker://docker.apporto.com/hyperstream-db:${DRONE_SOURCE_BRANCH//\//-}
  when:
    event:
      exclude:
      - tag


#------------------------------------------------------------
# Automagic DEV environments
#------------------------------------------------------------
- name: build_helm_dev
  image: containers-dev.apporto.com/images/bender
  environment:
    GITOPS_REPO:
      from_secret: GITOPS_REPO
    GITOPS_SSH_KEY:
      from_secret: GITOPS_SSH_KEY
  commands:
    - export IMAGE_TAG=${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    - export IMAGE_NAME=containers-dev.apporto.com/images/hyperstream
    - >-
        bender-build-helm
        --name hyperstream
        --merge-release-values
        --release-values-prefix hyperstream
        --image-name $IMAGE_NAME
        --image-tag $IMAGE_TAG
        --custom-release-values '{"db": {"image": {"repository": "containers-dev.apporto.com/images/hyperstream-db", "tag": "'$IMAGE_TAG'"}}}'
        --helm-src-dir "./apporto/helm"
        --group-name hyperstream
        --platform ${DRONE_SOURCE_BRANCH}
        --auto-dev-appset region-apps/streaming-appset.yaml
        --auto-dev-platform-src qa
        --commit
  when:
    branch:
      - dev/*/*

#------------------------------------------------------------
# Automagic Hotfix environments
#------------------------------------------------------------
- name: build_helm_hotfix
  image: containers-dev.apporto.com/images/bender
  environment:
    GITOPS_REPO:
      from_secret: GITOPS_REPO
    GITOPS_SSH_KEY:
      from_secret: GITOPS_SSH_KEY
  commands:
    - export IMAGE_TAG=${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    - export IMAGE_NAME=containers-dev.apporto.com/images/hyperstream
    - >-
        bender-build-helm
        --name hyperstream
        --merge-release-values
        --release-values-prefix hyperstream
        --image-name $IMAGE_NAME
        --image-tag $IMAGE_TAG
        --custom-release-values '{"db": {"image": {"repository": "containers-dev.apporto.com/images/hyperstream-db", "tag": "'$IMAGE_TAG'"}}}'
        --helm-src-dir "./apporto/helm"
        --group-name hyperstream
        --platform ${DRONE_SOURCE_BRANCH}
        --auto-dev-appset region-apps/streaming-appset.yaml
        --auto-dev-platform-src prod
        --commit
  when:
    branch:
      - hotfix/*/*

#------------------------------------------------------------
# Auto Deploy main branch stages
#------------------------------------------------------------
- name: build_helm_qa
  image: containers-dev.apporto.com/images/bender
  environment:
    GITOPS_REPO:
      from_secret: GITOPS_REPO
    GITOPS_SSH_KEY:
      from_secret: GITOPS_SSH_KEY
  commands:
    - export IMAGE_TAG=${DRONE_SOURCE_BRANCH//\//-}-${DRONE_COMMIT_SHA:0:8}
    - export IMAGE_NAME=containers-dev.apporto.com/images/hyperstream
    - >-
        bender-build-helm
        --name hyperstream
        --merge-release-values
        --release-values-prefix hyperstream
        --image-name $IMAGE_NAME
        --image-tag $IMAGE_TAG
        --custom-release-values '{"db": {"image": {"repository": "containers-dev.apporto.com/images/hyperstream-db", "tag": "'$IMAGE_TAG'"}}}'
        --helm-src-dir "./apporto/helm"
        --group-name hyperstream
        --platform qa
        --commit
  when:
    branch:
      - main

#------------------------------------------------------------
# Release (tag) stages
#------------------------------------------------------------
- name: buildtag-hyperstream-docker
  image: plugins/docker
  network_mode: bridge
  settings:
    auto_tag: true
    username:
      from_secret: CONTAINERS_DEV_USER
    password:
      from_secret: CONTAINERS_DEV_PASS
    repo: containers-dev.apporto.com/images/hyperstream
    registry: containers-dev.apporto.com
    dockerfile: Dockerfile.apporto
    build_args:
      - GIT_COMMIT=${DRONE_COMMIT_SHA}
      - GIT_TAGS=${DRONE_TAG}
    build_args_from_env:
      - DRONE_REPO_NAME
      - DRONE_SOURCE_BRANCH
      - DRONE_COMMIT_SHA
      - DRONE_TAG
      - DRONE_COMMIT_REF
      - DRONE_BUILD_NUMBER
      - APP_BUILD_VERSION
  when:
    event:
      - tag

- name: buildtag-hyperstream-db-docker
  image: plugins/docker
  network_mode: bridge
  settings:
    auto_tag: true
    username:
      from_secret: CONTAINERS_DEV_USER
    password:
      from_secret: CONTAINERS_DEV_PASS
    repo: containers-dev.apporto.com/images/hyperstream-db
    registry: containers-dev.apporto.com
    dockerfile: Dockerfile.apporto_mysql
  when:
    event:
      - tag


- name: copy-release-to-nexus
  image: containers-dev.apporto.com/tools/skopeo
  environment:
    CONTAINERS_DEV_USER:
      from_secret: CONTAINERS_DEV_USER
    CONTAINERS_DEV_PASS:
      from_secret: CONTAINERS_DEV_PASS
    DOCKER_USER:
      from_secret: DOCKER_USER
    DOCKER_PASS:
      from_secret: DOCKER_PASS
  commands:
    - export IMAGE_TAG=${DRONE_TAG##v}
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream:$IMAGE_TAG 
      docker://docker.apporto.com/hyperstream:$IMAGE_TAG
    - skopeo copy 
      --src-creds $CONTAINERS_DEV_USER:$CONTAINERS_DEV_PASS 
      --dest-creds $DOCKER_USER:$DOCKER_PASS 
      docker://containers-dev.apporto.com/images/hyperstream-db:$IMAGE_TAG 
      docker://docker.apporto.com/hyperstream-db:$IMAGE_TAG
  when:
    event:
      - tag



- name: build_helm_release
  image: containers-dev.apporto.com/images/bender
  environment:
    GITOPS_REPO:
      from_secret: GITOPS_REPO
    GITOPS_SSH_KEY:
      from_secret: GITOPS_SSH_KEY
  commands:
    - export IMAGE_TAG=${DRONE_TAG##v}
    - export IMAGE_NAME=containers.apporto.com/images/hyperstream
    - >-
        bender-build-helm
        --name hyperstream
        --merge-release-values
        --release-values-prefix hyperstream
        --image-name $IMAGE_NAME
        --image-tag $IMAGE_TAG
        --custom-release-values '{"db": {"image": {"repository": "containers.apporto.com/images/hyperstream-db", "tag": "'$IMAGE_TAG'"}}}'
        --helm-src-dir "./apporto/helm"
        --group-name hyperstream
        --platform prod
        --commit
  when:
    event:
      - tag

volumes:
- name: m2_cache
  host:
    path: /srv/m2_cache
