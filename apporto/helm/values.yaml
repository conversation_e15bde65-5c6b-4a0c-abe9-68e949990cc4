# Default values for hyperstream.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

fullnameOverride: hyperstream

k8s_cluster: {}

# This will set a number of variables needed by guac.
# right now, guac can't handle this number changing dynamically.
# XXX: ToDo - fix guac to be able to use this number dynamically.
replicaCount: 2

sidecarLogging: {}

#statefulSetServiceName: hyperstream1

# Provide defaults for non-secret information
apporto_config:
  guac_encrypt_secret: "REPLACEME"
  rollbar_access_token: "REPLACEME"
  timestamp_age_limit: "3600000"
  messenger_server: "kurento-messenger.apporto.com"
  dc_server_id: "nv-dc1"
  snapshot_username: "sshd.admin"
  snapshot_port: "22122"
  region: "nv"
  filebrowser:
    api_key: "REPLACEME"
    server: "filebrowser.localdomain"
  chatbot:
    server: "chatbot.localdomain"
  prevent_caps_lock: "false"
  allow_h264_frame_drop: "true"
  rds_logout_event_delay: 12
  #hap_capacity_api:
  #  api_secret_key: REPLACEME
  #  url: http://hap-capacity-api.api-microservices-prod.svc:5000/rdp/new-connection
  #  reboot_url: http://hap-capacity-api.api-microservices-prod.svc:5000/rdp/restart-instance
  #  max_hap_capacity_timeout: 360000
  #  max_allowed_failed_start: 3
  #otel_api:
  #  metrics_interval: 30
  #  collector_endpoint: http://apporto-otel-collector.eck.svc:4317
  rdp_router_api:
    service_url: http://rdp-router.api-microservices-prod.svc:3344
    secret_key: SOME_KEY
    timeout: 15
    attempt_count: 3
    keep_alive_interval: 15
  redis_thumb_db: "1"
  virtual_usb:
    grpc_server: vusb-service.localdomain
  sso_cert_gen:
    server: "http://cert-gen-server.apporto.com:3000/"
    api_key: "REPLACEME"

# Set these in deployment or region specific values files.
#  snapshot_scripts:
#    enumerate: "get_restorepoint_user_profile.ps1"
#    restore: "restore_user_profile.ps1"
#    backup: "backup_user_profile.ps1"
#  
#  vm_scripts:
#    backup: "aws_vdi-backup-v1.ps1"
#    restore: "aws_vdi-restore-v1.ps1"
#
#  # Haproxy API URL for capacity gateway:
#  haproxy_server: "https://msnv-api-staging.apporto.com/hap-capacity-api/rdp/new-connection"
#
#  # One-Time-URL settings:
#  otu_settings:
#    redis_url: "REDIS_URL"
#    default_otu_check: "True"


hyperstream:
  env:
    GUAC_LOG_LEVEL: INFO
    # Java 11 GC options:
    JAVA_OPTS: "-Xlog:gc=debug:file=logs/gc.log:time,uptime,level,tags:filecount=2,filesize=30m"
    CUSTOM_TRANSLATIONS_HOME: "/srv/hyperstream/translations"
    OMI_SKIP_CA_CHECK: "1"
    OMI_SKIP_CN_CHECK: "1"
  image:
    repository: containers-dev.apporto.com/images/hyperstream
    tag: develop
    #pullPolicy: ifNotPresent
    pullPolicy: Always
    command: ['dumb-init']
    args: [ '--', '/entrypoint.sh']
  service:
    type: ClusterIP
    port: 8080
  limits:
    # Xss - thread stack size: 1 M from   java -XX:+PrintFlagsFinal -version | grep ThreadStackSize  (in KB)
    # Max memory = [-Xmx] + [-XX:MaxMetaspaceSize] + number_of_threads * [-Xss]
    # XXX: ToDo make max_memory a calculation in the template.
    max_memory: 4624
    Xmx_size: 4096
    metaspace_size: 128
    max_threads: 400

ingress:
  enabled: true
  path: /hyperstream
  hostname: some-hyperstream.apporto.com
  tlsSecretName: star-apporto-com-tls
  ingressClassName: nginx
  annotations:
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/session-cookie-hash: sha1
    nginx.ingress.kubernetes.io/session-cookie-name: hyperstream-LB

db:
  env:
    MYSQL_ROOT_PASSWORD: REPLACE_ROOTPW
    MYSQL_DATABASE: guacamole_db
    MYSQL_USER: REPLACE_USER
    MYSQL_PASSWORD: REPLACE_PASS
  poolCfgs:
    MYSQL_MAX_ACTIVE_CONNECTIONS: 30
    MYSQL_MAX_IDLE_CONNECTIONS: 5
    MYSQL_MAX_CHECKOUT_TIME: 60000
    MYSQL_TIME_TO_WAIT: 20000
    MYSQL_PING_CONNECTIONS_NOT_USED_FOR: 20000
  image:
    repository: containers-dev.apporto.com/images/hyperstream-db
    tag: develop
    pullPolicy: Always
    #pullPolicy: ifNotPresent
  limits:
    max_memory: 4624

guacd:
  env:
    WIN_DOMAIN: SOME-DOMAIN.COM
    WIN_DOMAIN_PDC: PDC.SOME-DOMAIN.COM
    WIN_DOMAIN_ROOT_CERT: FILE:/srv/win-root-cert/cert.pem
  image:
    repository: containers-dev.apporto.com/images/guacd
    tag: develop
    pullPolicy: Always
    #pullPolicy: ifNotPresent
    #command: ['dumb-init']
    #args: [ '--', '/entrypoint.sh']
  limits:
    max_memory: 4624

container:
  imagePullSecrets:
  - name: apporto-container-creds

deployment:
  annotations: {}

#configMapDirs:
#  srv-config:
#    hostDir: /srv
#    srcConfigMapName: hyperstream-configs

# like configmaps
k8sSecretMounts: 
  win-root-cert:
    filename: "cert.pem"
    data: "REPLACE_WITH_CERT_DATA"

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 128Mi
  # requests:
  #  cpu: 100m
  #  memory: 128Mi

nodeSelector: {}

tolerations: []

affinity:
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
    - labelSelector:
        matchExpressions:
        - key: app
          operator: In
          values:
          - hyperstream
      topologyKey: "kubernetes.io/hostname"


cronjobs: {}
